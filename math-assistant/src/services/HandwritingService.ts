import { Stroke, HandwritingRecognitionResult } from '../types';

export class HandwritingService {
  private apiKey: string | null = null;
  private baseUrl = 'https://cloud.myscript.com/api/v4.0/iink';

  constructor() {
    // In a real implementation, this would come from environment variables
    this.apiKey = process.env.REACT_APP_MYSCRIPT_API_KEY || null;
  }

  async recognizeStrokes(strokes: Stroke[]): Promise<HandwritingRecognitionResult> {
    if (!this.apiKey) {
      // Fallback to mock recognition for development
      return this.mockRecognition(strokes);
    }

    try {
      const inkData = this.convertStrokesToInkData(strokes);
      
      const response = await fetch(`${this.baseUrl}/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          configuration: {
            lang: 'en_US',
            export: {
              'application/x-latex': {}
            }
          },
          contentType: 'Math',
          strokeGroups: [
            {
              strokes: inkData
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`Recognition failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        text: result.exports?.['application/x-latex'] || '',
        confidence: this.calculateConfidence(result),
        alternatives: this.extractAlternatives(result)
      };
    } catch (error) {
      console.error('Handwriting recognition error:', error);
      return this.mockRecognition(strokes);
    }
  }

  private convertStrokesToInkData(strokes: Stroke[]): any[] {
    return strokes.map(stroke => ({
      id: stroke.id,
      pointerType: 'PEN',
      pointerId: 1,
      x: stroke.points.map(p => p.x),
      y: stroke.points.map(p => p.y),
      t: stroke.points.map((_, i) => stroke.timestamp + i * 10), // Mock timestamps
      p: stroke.points.map(() => 0.5) // Mock pressure
    }));
  }

  private calculateConfidence(result: any): number {
    // Extract confidence from MyScript result
    // This is a simplified implementation
    return result.confidence || 0.8;
  }

  private extractAlternatives(result: any): Array<{ text: string; confidence: number }> {
    // Extract alternative interpretations from MyScript result
    // This is a simplified implementation
    return result.alternatives || [];
  }

  private mockRecognition(strokes: Stroke[]): HandwritingRecognitionResult {
    // Mock recognition for development/testing
    const mockExpressions = [
      'x + 2 = 5',
      '2x^2 + 3x - 1',
      'sin(x) + cos(x)',
      'y = mx + b',
      '\\frac{1}{2}x + 3',
      'x^2 - 4x + 4 = 0',
      '\\sqrt{x + 1}',
      '\\int x^2 dx'
    ];

    // Simple heuristic based on stroke count and complexity
    const strokeCount = strokes.length;
    const totalPoints = strokes.reduce((sum, stroke) => sum + stroke.points.length, 0);
    
    let selectedExpression: string;
    let confidence: number;

    if (strokeCount === 1 && totalPoints < 10) {
      // Simple character or number
      selectedExpression = Math.random() > 0.5 ? 'x' : '2';
      confidence = 0.95;
    } else if (strokeCount <= 3) {
      // Simple expression
      selectedExpression = mockExpressions[Math.floor(Math.random() * 4)];
      confidence = 0.85;
    } else {
      // Complex expression
      selectedExpression = mockExpressions[Math.floor(Math.random() * mockExpressions.length)];
      confidence = 0.75;
    }

    return {
      text: selectedExpression,
      confidence,
      alternatives: [
        { text: selectedExpression, confidence },
        { text: mockExpressions[Math.floor(Math.random() * mockExpressions.length)], confidence: confidence - 0.1 }
      ]
    };
  }

  async recognizeMathExpression(strokes: Stroke[]): Promise<HandwritingRecognitionResult> {
    // Specialized method for mathematical expressions
    const result = await this.recognizeStrokes(strokes);
    
    // Post-process to improve mathematical notation
    result.text = this.improveMathNotation(result.text);
    
    return result;
  }

  private improveMathNotation(text: string): string {
    // Convert common patterns to proper mathematical notation
    let improved = text;
    
    // Convert fractions
    improved = improved.replace(/(\d+)\/(\d+)/g, '\\frac{$1}{$2}');
    
    // Convert exponents
    improved = improved.replace(/\^(\d+)/g, '^{$1}');
    improved = improved.replace(/\^([a-zA-Z]+)/g, '^{$1}');
    
    // Convert square roots
    improved = improved.replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}');
    
    // Convert integrals
    improved = improved.replace(/int\s+/g, '\\int ');
    
    // Convert Greek letters
    improved = improved.replace(/\balpha\b/g, '\\alpha');
    improved = improved.replace(/\bbeta\b/g, '\\beta');
    improved = improved.replace(/\bgamma\b/g, '\\gamma');
    improved = improved.replace(/\bdelta\b/g, '\\delta');
    improved = improved.replace(/\bpi\b/g, '\\pi');
    improved = improved.replace(/\btheta\b/g, '\\theta');
    
    return improved;
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  isConfigured(): boolean {
    return this.apiKey !== null;
  }
}
