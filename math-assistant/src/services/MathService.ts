import { evaluate, parse, simplify, derivative, format } from 'mathjs';
import { Variable, SolutionStep, ErrorDetectionResult } from '../types';

export class MathService {
  private variables: Map<string, Variable> = new Map();
  private scope: any = {};

  parseExpression(expression: string): { parsed: any; isValid: boolean; error?: string } {
    try {
      const parsed = parse(expression);
      return { parsed, isValid: true };
    } catch (error) {
      return { 
        parsed: null, 
        isValid: false, 
        error: error instanceof Error ? error.message : 'Unknown parsing error'
      };
    }
  }

  evaluateExpression(expression: string): { result: any; isValid: boolean; error?: string } {
    try {
      const result = evaluate(expression, this.scope);
      return { result, isValid: true };
    } catch (error) {
      return { 
        result: null, 
        isValid: false, 
        error: error instanceof Error ? error.message : 'Unknown evaluation error'
      };
    }
  }

  simplifyExpression(expression: string): { simplified: string; isValid: boolean; error?: string } {
    try {
      const parsed = parse(expression);
      const simplified = simplify(parsed);
      return { simplified: format(simplified), isValid: true };
    } catch (error) {
      return { 
        simplified: expression, 
        isValid: false, 
        error: error instanceof Error ? error.message : 'Unknown simplification error'
      };
    }
  }

  solveStepByStep(expression: string): SolutionStep[] {
    const steps: SolutionStep[] = [];
    
    try {
      // Parse the expression
      const parsed = parse(expression);
      
      // Step 1: Original expression
      steps.push({
        id: `step_${Date.now()}_1`,
        expression: expression,
        result: expression,
        explanation: 'Original expression',
        isCorrect: true
      });

      // Step 2: Simplify if possible
      const simplified = simplify(parsed);
      const simplifiedStr = format(simplified);
      
      if (simplifiedStr !== expression) {
        steps.push({
          id: `step_${Date.now()}_2`,
          expression: simplifiedStr,
          result: simplifiedStr,
          explanation: 'Simplified expression',
          isCorrect: true
        });
      }

      // Step 3: Evaluate if possible
      try {
        const result = evaluate(expression, this.scope);
        steps.push({
          id: `step_${Date.now()}_3`,
          expression: simplifiedStr || expression,
          result: String(result),
          explanation: 'Final result',
          isCorrect: true
        });
      } catch (evalError) {
        // If evaluation fails, it might be an equation to solve
        steps.push(...this.solveEquation(expression));
      }

    } catch (error) {
      steps.push({
        id: `step_${Date.now()}_error`,
        expression: expression,
        result: 'Error',
        explanation: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isCorrect: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    }

    return steps;
  }

  private solveEquation(equation: string): SolutionStep[] {
    const steps: SolutionStep[] = [];
    
    // Check if it's an equation (contains =)
    if (!equation.includes('=')) {
      return steps;
    }

    try {
      const [leftSide, rightSide] = equation.split('=').map(s => s.trim());
      
      // Simple linear equation solver
      if (this.isLinearEquation(leftSide, rightSide)) {
        steps.push(...this.solveLinearEquation(leftSide, rightSide));
      } else if (this.isQuadraticEquation(leftSide, rightSide)) {
        steps.push(...this.solveQuadraticEquation(leftSide, rightSide));
      } else {
        steps.push({
          id: `step_${Date.now()}_unsupported`,
          expression: equation,
          result: 'Unsupported equation type',
          explanation: 'This equation type is not yet supported for step-by-step solving',
          isCorrect: false
        });
      }
    } catch (error) {
      steps.push({
        id: `step_${Date.now()}_error`,
        expression: equation,
        result: 'Error',
        explanation: `Error solving equation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isCorrect: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    }

    return steps;
  }

  private isLinearEquation(left: string, right: string): boolean {
    // Simple check for linear equations (contains x but no x^2, x^3, etc.)
    const combined = left + right;
    return combined.includes('x') && !combined.match(/x\s*\^\s*[2-9]/);
  }

  private isQuadraticEquation(left: string, right: string): boolean {
    // Simple check for quadratic equations (contains x^2)
    const combined = left + right;
    return combined.match(/x\s*\^\s*2/) !== null;
  }

  private solveLinearEquation(left: string, right: string): SolutionStep[] {
    const steps: SolutionStep[] = [];
    
    try {
      // Example: 2x + 3 = 7
      // Step 1: Move constants to right side
      steps.push({
        id: `step_${Date.now()}_1`,
        expression: `${left} = ${right}`,
        result: `${left} = ${right}`,
        explanation: 'Original equation',
        isCorrect: true
      });

      // This is a simplified implementation
      // In a real implementation, you would parse the equation more thoroughly
      const result = this.evaluateExpression(`solve(${left} = ${right}, x)`);
      
      if (result.isValid) {
        steps.push({
          id: `step_${Date.now()}_final`,
          expression: 'x',
          result: String(result.result),
          explanation: 'Solution for x',
          isCorrect: true
        });
      }
    } catch (error) {
      steps.push({
        id: `step_${Date.now()}_error`,
        expression: `${left} = ${right}`,
        result: 'Error',
        explanation: `Error solving linear equation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isCorrect: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    }

    return steps;
  }

  private solveQuadraticEquation(left: string, right: string): SolutionStep[] {
    const steps: SolutionStep[] = [];
    
    // Simplified quadratic equation solver
    steps.push({
      id: `step_${Date.now()}_1`,
      expression: `${left} = ${right}`,
      result: `${left} = ${right}`,
      explanation: 'Original quadratic equation',
      isCorrect: true
    });

    steps.push({
      id: `step_${Date.now()}_2`,
      expression: 'Quadratic formula: x = (-b ± √(b² - 4ac)) / 2a',
      result: 'Formula applied',
      explanation: 'Apply quadratic formula',
      isCorrect: true
    });

    return steps;
  }

  detectErrors(expression: string, context?: any): ErrorDetectionResult {
    try {
      // Syntax check
      const parseResult = this.parseExpression(expression);
      if (!parseResult.isValid) {
        return {
          hasError: true,
          errorType: 'syntax',
          message: parseResult.error || 'Syntax error in expression',
          suggestion: 'Check for missing operators, parentheses, or invalid characters'
        };
      }

      // Mathematical validity check
      const evalResult = this.evaluateExpression(expression);
      if (!evalResult.isValid) {
        return {
          hasError: true,
          errorType: 'mathematical',
          message: evalResult.error || 'Mathematical error in expression',
          suggestion: 'Check for division by zero, undefined operations, or invalid domains'
        };
      }

      // Logical consistency check (if context is provided)
      if (context && context.previousStep) {
        const logicalCheck = this.checkLogicalConsistency(expression, context.previousStep);
        if (!logicalCheck.isValid) {
          return {
            hasError: true,
            errorType: 'logical',
            message: logicalCheck.error || 'Logical error in step',
            suggestion: 'This step does not follow logically from the previous step'
          };
        }
      }

      return {
        hasError: false,
        errorType: 'syntax',
        message: 'No errors detected'
      };
    } catch (error) {
      return {
        hasError: true,
        errorType: 'syntax',
        message: error instanceof Error ? error.message : 'Unknown error',
        suggestion: 'Please check the expression syntax'
      };
    }
  }

  private checkLogicalConsistency(currentStep: string, previousStep: string): { isValid: boolean; error?: string } {
    // Simplified logical consistency check
    // In a real implementation, this would be much more sophisticated
    try {
      const current = this.evaluateExpression(currentStep);
      const previous = this.evaluateExpression(previousStep);
      
      if (current.isValid && previous.isValid) {
        // Check if the values are approximately equal (for algebraic manipulations)
        const diff = Math.abs(Number(current.result) - Number(previous.result));
        if (diff > 0.0001) {
          return {
            isValid: false,
            error: 'Step does not maintain equality'
          };
        }
      }
      
      return { isValid: true };
    } catch (error) {
      return { isValid: true }; // If we can't evaluate, assume it's valid
    }
  }

  addVariable(variable: Variable): void {
    this.variables.set(variable.name, variable);
    this.scope[variable.name] = variable.value;
  }

  getVariable(name: string): Variable | undefined {
    return this.variables.get(name);
  }

  getAllVariables(): Variable[] {
    return Array.from(this.variables.values());
  }

  removeVariable(name: string): void {
    this.variables.delete(name);
    delete this.scope[name];
  }

  updateScope(): void {
    this.scope = {};
    for (const [name, variable] of this.variables) {
      this.scope[name] = variable.value;
    }
  }
}
