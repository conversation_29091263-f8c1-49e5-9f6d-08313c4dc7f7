// Core types for the Math Assistant application

export interface Point {
  x: number;
  y: number;
}

export interface Stroke {
  id: string;
  points: Point[];
  timestamp: number;
  color: string;
  width: number;
}

export interface MathExpression {
  id: string;
  originalStrokes: Stroke[];
  recognizedText: string;
  parsedExpression: any; // Math.js expression object
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isValid: boolean;
  errors?: string[];
}

export interface Variable {
  name: string;
  value: number | string | any;
  type: 'number' | 'expression' | 'function' | 'unit';
  scope: 'local' | 'global';
  createdAt: number;
  lastUsed: number;
}

export interface SolutionStep {
  id: string;
  expression: string;
  result: string;
  explanation: string;
  isCorrect: boolean;
  errors?: string[];
  handwrittenResult?: Stroke[];
}

export interface Problem {
  id: string;
  originalExpression: MathExpression;
  steps: SolutionStep[];
  variables: Variable[];
  isComplete: boolean;
  createdAt: number;
}

export interface CanvasState {
  strokes: Stroke[];
  expressions: MathExpression[];
  variables: Variable[];
  problems: Problem[];
  selectedTool: 'pen' | 'eraser' | 'select';
  zoom: number;
  pan: Point;
}

export interface ErrorDetectionResult {
  hasError: boolean;
  errorType: 'syntax' | 'logical' | 'mathematical';
  message: string;
  suggestion?: string;
  correctedExpression?: string;
}

export interface HandwritingRecognitionResult {
  text: string;
  confidence: number;
  alternatives?: Array<{
    text: string;
    confidence: number;
  }>;
}

export interface CollaborationEvent {
  type: 'stroke_added' | 'stroke_removed' | 'expression_added' | 'variable_created';
  data: any;
  userId: string;
  timestamp: number;
}
