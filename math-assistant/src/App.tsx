import React, { useState, useCallback } from 'react';
import { CanvasManager } from './components/Canvas/CanvasManager';
import { MathService } from './services/MathService';
import { Stroke, MathExpression, Variable, SolutionStep } from './types';
import './App.css';

function App() {
  const [expressions, setExpressions] = useState<MathExpression[]>([]);
  const [variables, setVariables] = useState<Variable[]>([]);
  const [currentProblem, setCurrentProblem] = useState<SolutionStep[]>([]);
  const [selectedExpression, setSelectedExpression] = useState<string>('');

  const mathService = new MathService();

  const handleStrokeAdded = useCallback((stroke: Stroke) => {
    console.log('Stroke added:', stroke);
  }, []);

  const handleExpressionRecognized = useCallback((expression: any) => {
    console.log('Expression recognized:', expression);

    const mathExpression: MathExpression = {
      id: `expr_${Date.now()}`,
      originalStrokes: expression.originalStrokes,
      recognizedText: expression.recognizedText,
      parsedExpression: expression.parsedExpression,
      boundingBox: { x: 0, y: 0, width: 100, height: 50 }, // Mock bounding box
      isValid: expression.isValid
    };

    setExpressions(prev => [...prev, mathExpression]);

    // Auto-solve if it's a complete expression
    if (expression.isValid) {
      solveExpression(expression.recognizedText);
    }
  }, []);

  const solveExpression = (expression: string) => {
    const steps = mathService.solveStepByStep(expression);
    setCurrentProblem(steps);
    setSelectedExpression(expression);
  };

  const addVariable = (name: string, value: number) => {
    const variable: Variable = {
      name,
      value,
      type: 'number',
      scope: 'global',
      createdAt: Date.now(),
      lastUsed: Date.now()
    };

    setVariables(prev => [...prev, variable]);
    mathService.addVariable(variable);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Math Assistant</h1>
        <p>Write mathematical expressions and get step-by-step solutions</p>
      </header>

      <main className="app-main">
        <div className="canvas-section">
          <h2>Drawing Canvas</h2>
          <CanvasManager
            width={800}
            height={600}
            onStrokeAdded={handleStrokeAdded}
            onExpressionRecognized={handleExpressionRecognized}
          />
        </div>

        <div className="sidebar">
          <div className="expressions-panel">
            <h3>Recognized Expressions</h3>
            <div className="expressions-list">
              {expressions.map(expr => (
                <div
                  key={expr.id}
                  className={`expression-item ${expr.isValid ? 'valid' : 'invalid'}`}
                  onClick={() => solveExpression(expr.recognizedText)}
                >
                  <span className="expression-text">{expr.recognizedText}</span>
                  <span className="expression-status">
                    {expr.isValid ? '✓' : '✗'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="variables-panel">
            <h3>Variables</h3>
            <div className="variables-list">
              {variables.map(variable => (
                <div key={variable.name} className="variable-item">
                  <span className="variable-name">{variable.name}</span>
                  <span className="variable-value">{String(variable.value)}</span>
                </div>
              ))}
            </div>
            <div className="add-variable">
              <input
                type="text"
                placeholder="Variable name"
                id="var-name"
              />
              <input
                type="number"
                placeholder="Value"
                id="var-value"
              />
              <button onClick={() => {
                const nameInput = document.getElementById('var-name') as HTMLInputElement;
                const valueInput = document.getElementById('var-value') as HTMLInputElement;
                if (nameInput.value && valueInput.value) {
                  addVariable(nameInput.value, parseFloat(valueInput.value));
                  nameInput.value = '';
                  valueInput.value = '';
                }
              }}>
                Add Variable
              </button>
            </div>
          </div>

          <div className="solution-panel">
            <h3>Step-by-Step Solution</h3>
            {selectedExpression && (
              <div className="selected-expression">
                Solving: <strong>{selectedExpression}</strong>
              </div>
            )}
            <div className="solution-steps">
              {currentProblem.map((step, index) => (
                <div
                  key={step.id}
                  className={`solution-step ${step.isCorrect ? 'correct' : 'incorrect'}`}
                >
                  <div className="step-number">Step {index + 1}</div>
                  <div className="step-expression">{step.expression}</div>
                  <div className="step-result">{step.result}</div>
                  <div className="step-explanation">{step.explanation}</div>
                  {step.errors && (
                    <div className="step-errors">
                      {step.errors.map((error, i) => (
                        <div key={i} className="error">{error}</div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
