.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.app-header {
  background: #f8f9fa;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.app-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

.app-header p {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.canvas-section {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.canvas-section h2 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.canvas-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.drawing-canvas {
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
}

.canvas-controls {
  display: flex;
  gap: 0.5rem;
}

.canvas-controls button {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.canvas-controls button:hover {
  background: #f8f9fa;
}

.sidebar {
  width: 350px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.expressions-panel,
.variables-panel,
.solution-panel {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.expressions-panel h3,
.variables-panel h3,
.solution-panel h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.expressions-list,
.variables-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.expression-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.expression-item:hover {
  background: #e9ecef;
}

.expression-item.valid {
  border-color: #28a745;
}

.expression-item.invalid {
  border-color: #dc3545;
}

.expression-text {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.expression-status {
  font-weight: bold;
}

.expression-item.valid .expression-status {
  color: #28a745;
}

.expression-item.invalid .expression-status {
  color: #dc3545;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
}

.variable-name {
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.variable-value {
  color: #6c757d;
  font-family: 'Courier New', monospace;
}

.add-variable {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
}

.add-variable input {
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.9rem;
}

.add-variable button {
  padding: 0.5rem;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-variable button:hover {
  background: #0056b3;
}

.selected-expression {
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #e3f2fd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.solution-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.solution-step {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
}

.solution-step.correct {
  border-color: #28a745;
  background: #f8fff9;
}

.solution-step.incorrect {
  border-color: #dc3545;
  background: #fff8f8;
}

.step-number {
  font-weight: bold;
  color: #6c757d;
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
}

.step-expression {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.step-result {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.step-explanation {
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
}

.step-errors {
  margin-top: 0.5rem;
}

.error {
  color: #dc3545;
  font-size: 0.8rem;
  background: #f8d7da;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  margin-bottom: 0.25rem;
}
