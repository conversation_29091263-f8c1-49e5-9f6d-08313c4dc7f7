import React, { useRef, useEffect, useState, useCallback } from 'react';
import { fabric } from 'fabric';
import { Point, Stroke, CanvasState } from '../../types';
import { HandwritingService } from '../../services/HandwritingService';
import { MathService } from '../../services/MathService';

interface CanvasManagerProps {
  width: number;
  height: number;
  onStrokeAdded?: (stroke: Stroke) => void;
  onExpressionRecognized?: (expression: any) => void;
}

export const CanvasManager: React.FC<CanvasManagerProps> = ({
  width,
  height,
  onStrokeAdded,
  onExpressionRecognized
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentStroke, setCurrentStroke] = useState<Point[]>([]);
  const [strokes, setStrokes] = useState<Stroke[]>([]);

  const handwritingService = new HandwritingService();
  const mathService = new MathService();

  useEffect(() => {
    if (canvasRef.current && !fabricCanvasRef.current) {
      // Initialize Fabric.js canvas
      fabricCanvasRef.current = new fabric.Canvas(canvasRef.current, {
        width,
        height,
        isDrawingMode: true,
        freeDrawingBrush: new fabric.PencilBrush(fabricCanvasRef.current)
      });

      const canvas = fabricCanvasRef.current;
      
      // Configure drawing brush
      canvas.freeDrawingBrush.width = 2;
      canvas.freeDrawingBrush.color = '#000000';

      // Handle drawing events
      canvas.on('path:created', handlePathCreated);
      canvas.on('mouse:down', handleMouseDown);
      canvas.on('mouse:move', handleMouseMove);
      canvas.on('mouse:up', handleMouseUp);
    }

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, [width, height]);

  const handlePathCreated = useCallback(async (event: any) => {
    const path = event.path;
    const pathData = path.path;
    
    // Convert Fabric.js path to our stroke format
    const points: Point[] = [];
    
    // Extract points from path data
    for (let i = 0; i < pathData.length; i++) {
      const segment = pathData[i];
      if (segment[0] === 'M' || segment[0] === 'L') {
        points.push({ x: segment[1], y: segment[2] });
      } else if (segment[0] === 'Q') {
        // Quadratic curve - add control point and end point
        points.push({ x: segment[3], y: segment[4] });
      }
    }

    const stroke: Stroke = {
      id: `stroke_${Date.now()}_${Math.random()}`,
      points,
      timestamp: Date.now(),
      color: path.stroke || '#000000',
      width: path.strokeWidth || 2
    };

    setStrokes(prev => [...prev, stroke]);
    onStrokeAdded?.(stroke);

    // Trigger handwriting recognition after a short delay
    setTimeout(() => {
      recognizeHandwriting([stroke]);
    }, 500);
  }, [onStrokeAdded]);

  const handleMouseDown = useCallback((event: any) => {
    if (fabricCanvasRef.current?.isDrawingMode) {
      setIsDrawing(true);
      const pointer = fabricCanvasRef.current.getPointer(event.e);
      setCurrentStroke([{ x: pointer.x, y: pointer.y }]);
    }
  }, []);

  const handleMouseMove = useCallback((event: any) => {
    if (isDrawing && fabricCanvasRef.current?.isDrawingMode) {
      const pointer = fabricCanvasRef.current.getPointer(event.e);
      setCurrentStroke(prev => [...prev, { x: pointer.x, y: pointer.y }]);
    }
  }, [isDrawing]);

  const handleMouseUp = useCallback(() => {
    if (isDrawing) {
      setIsDrawing(false);
      setCurrentStroke([]);
    }
  }, [isDrawing]);

  const recognizeHandwriting = async (strokesToRecognize: Stroke[]) => {
    try {
      // Group nearby strokes that might form a single expression
      const groupedStrokes = groupNearbyStrokes(strokesToRecognize);
      
      for (const group of groupedStrokes) {
        const result = await handwritingService.recognizeStrokes(group);
        
        if (result.confidence > 0.7) {
          // Parse the recognized text as a mathematical expression
          const expression = mathService.parseExpression(result.text);
          
          if (expression.isValid) {
            onExpressionRecognized?.({
              originalStrokes: group,
              recognizedText: result.text,
              parsedExpression: expression.parsed,
              isValid: true
            });
          }
        }
      }
    } catch (error) {
      console.error('Handwriting recognition failed:', error);
    }
  };

  const groupNearbyStrokes = (strokes: Stroke[]): Stroke[][] => {
    // Simple grouping algorithm - group strokes that are close to each other
    const groups: Stroke[][] = [];
    const processed = new Set<string>();
    
    for (const stroke of strokes) {
      if (processed.has(stroke.id)) continue;
      
      const group = [stroke];
      processed.add(stroke.id);
      
      // Find nearby strokes
      for (const otherStroke of strokes) {
        if (processed.has(otherStroke.id)) continue;
        
        if (areStrokesNearby(stroke, otherStroke)) {
          group.push(otherStroke);
          processed.add(otherStroke.id);
        }
      }
      
      groups.push(group);
    }
    
    return groups;
  };

  const areStrokesNearby = (stroke1: Stroke, stroke2: Stroke): boolean => {
    const threshold = 50; // pixels
    
    // Check if any point in stroke1 is close to any point in stroke2
    for (const point1 of stroke1.points) {
      for (const point2 of stroke2.points) {
        const distance = Math.sqrt(
          Math.pow(point1.x - point2.x, 2) + Math.pow(point1.y - point2.y, 2)
        );
        if (distance < threshold) {
          return true;
        }
      }
    }
    
    return false;
  };

  const clearCanvas = () => {
    if (fabricCanvasRef.current) {
      fabricCanvasRef.current.clear();
      setStrokes([]);
    }
  };

  const setDrawingMode = (enabled: boolean) => {
    if (fabricCanvasRef.current) {
      fabricCanvasRef.current.isDrawingMode = enabled;
    }
  };

  return (
    <div className="canvas-container">
      <canvas
        ref={canvasRef}
        className="drawing-canvas"
        style={{ border: '1px solid #ccc' }}
      />
      <div className="canvas-controls">
        <button onClick={clearCanvas}>Clear</button>
        <button onClick={() => setDrawingMode(true)}>Draw</button>
        <button onClick={() => setDrawingMode(false)}>Select</button>
      </div>
    </div>
  );
};
