# https://www.gitpod.io/docs/config-gitpod-file

tasks:
  - name: Build & Watch
    init: npm install
    command: npm run build -- -w -f

  - name: Node
    init: npm install
    command: npm start node -- --no-watch

  - name: Next.js
    init: npm install
    command: npm start next -- --no-watch

  - name: Vanilla
    init: npm install
    command: npm start vanilla -- --no-watch

  - name: Vue.js
    init: gp sync-await deps
    command: npm start vue -- --no-watch
