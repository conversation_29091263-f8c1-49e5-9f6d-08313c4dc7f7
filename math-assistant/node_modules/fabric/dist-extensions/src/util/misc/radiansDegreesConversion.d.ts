import type { TRadian, TDegree } from '../../typedefs';
/**
 * Transforms degrees to radians.
 * @param {TDegree} degrees value in degrees
 * @return {TRadian} value in radians
 */
export declare const degreesToRadians: (degrees: TDegree) => TRadian;
/**
 * Transforms radians to degrees.
 * @param {TRadian} radians value in radians
 * @return {TDegree} value in degrees
 */
export declare const radiansToDegrees: (radians: TRadian) => TDegree;
//# sourceMappingURL=radiansDegreesConversion.d.ts.map